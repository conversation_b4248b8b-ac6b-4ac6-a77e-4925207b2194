import socket from 'socket.io-client';

let socketInstance = null;

export const intializeSocket = (projectid) => {
    if (socketInstance) {
        return socketInstance;
    }

    socketInstance = socket('http://localhost:3000', {
        auth: {
            token: localStorage.getItem('token'),
            projectId: projectid
        },
        query: {
            projectId: projectid
        },
        extraHeaders: {
            projectid: projectid
        }
    });

    socketInstance.on('connect', () => {
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        console.log(`✅ Socket connected: ${user.email} | Project: ${projectid}`);
    });

    socketInstance.on('disconnect', (reason) => {
        console.log(`❌ Socket disconnected: ${reason}`);
    });

    socketInstance.on('connect_error', (error) => {
        console.error('🚫 Socket connection error:', error.message);
    });

    // Listen for user status responses
    socketInstance.on('user-status', (data) => {
        console.log('📊 User status:', data);
    });

    // Listen for active users list
    socketInstance.on('active-users', (users) => {
        console.log('👥 Active users:', users.length);
    });

    return socketInstance;
};

export const recieveMessage = (eventname, callback) => {
    if (!socketInstance) {
        console.error('❌ Socket not initialized for receiving messages');
        return;
    }
    socketInstance.on(eventname, callback);
};

export const sendMessage = (eventname, data) => {
    if (!socketInstance) {
        console.error('❌ Socket not initialized for sending messages');
        return;
    }
    socketInstance.emit(eventname, data);
};

// Check if a specific user is connected
export const checkUserStatus = (userId) => {
    if (!socketInstance) {
        console.error('❌ Socket not initialized for checking user status');
        return;
    }
    socketInstance.emit('check-user-status', userId);
};

// Get list of all active users
export const getActiveUsers = () => {
    if (!socketInstance) {
        console.error('❌ Socket not initialized for getting active users');
        return;
    }
    socketInstance.emit('get-active-users');
};

// Check if current socket is connected
export const isConnected = () => {
    return socketInstance && socketInstance.connected;
};

// Get current socket ID
export const getSocketId = () => {
    return socketInstance ? socketInstance.id : null;
};

// Disconnect socket
export const disconnectSocket = () => {
    if (socketInstance) {
        socketInstance.disconnect();
        socketInstance = null;
    }
};