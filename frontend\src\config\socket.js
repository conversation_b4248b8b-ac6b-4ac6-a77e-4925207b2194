import socket from 'socket.io-client';

let socketInstance = null;

export const intializeSocket = (projectid) => {
    if (socketInstance) {
        console.log('🔄 Socket already exists, returning existing instance');
        return socketInstance;
    }

    console.log('🔌 Creating new socket connection for project:', projectid);

    socketInstance = socket('http://localhost:3000', {
        auth: {
            token: localStorage.getItem('token')
        },
        extraHeaders: {
            projectId: projectid
        }
    });

    socketInstance.on('connect', () => {
        console.log('✅ Socket connected successfully:', socketInstance.id);
    });

    socketInstance.on('disconnect', () => {
        console.log('❌ Socket disconnected');
    });

    socketInstance.on('connect_error', (error) => {
        console.error('🚫 Socket connection error:', error);
    });

    return socketInstance;
};

export const recieveMessage = (eventname, callback) => {
    if (!socketInstance) {
        console.error('❌ Socket not initialized for receiving messages');
        return;
    }
    console.log('👂 Setting up listener for event:', eventname);
    socketInstance.on(eventname, callback);
};

export const sendMessage = (eventname, data) => {
    if (!socketInstance) {
        console.error('❌ Socket not initialized for sending messages');
        return;
    }
    console.log('📤 Sending message via socket:', eventname, data);
    socketInstance.emit(eventname, data);
};