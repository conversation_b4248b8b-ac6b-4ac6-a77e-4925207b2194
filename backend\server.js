import dotenv from 'dotenv';
dotenv.config();
import http from 'http';
import app from './app.js';
import client from './services/redis.services.js';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import projectModel from './models/project.model.js';

mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });

const server = http.createServer(app);

const io = new Server(server,{
    cors: {
        origin: '*',
        methods: ['GET', 'POST']
    }
});

// Track active users
const activeUsers = new Map(); 

io.use(async (socket,next)=>{
    try{
        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.split(' ')[1];
        const projectId = socket.handshake.headers?.projectid || socket.handshake.query?.projectId || socket.handshake.auth?.projectId;

        // console.log('🔐 Socket authentication attempt:', {
        //     hasToken: !!token,
        //     projectId: projectId,
        //     headers: Object.keys(socket.handshake.headers),
        //     auth: Object.keys(socket.handshake.auth || {})
        // });

        if(!token){
            return next(new Error('Unauthorized - No token provided'));
        }

        const decoded = jwt.verify(token,process.env.JWT_SECRET);
        if(!decoded){
            return next(new Error('Unauthorized - Invalid token'));
        }
        socket.user = decoded;

        // Only validate project if projectId is provided
        if(projectId){
            if(!mongoose.Types.ObjectId.isValid(projectId)){
                return next(new Error('Invalid project ID format'));
            }

            socket.project = await projectModel.findById(projectId);
            if(!socket.project){
                return next(new Error('Project not found'));
            }
        } else {
            // Allow connection without project for testing
            socket.project = { _id: 'no-project', name: 'No Project' };
        }

        next();

    }catch(error){
        console.error('❌ Socket authentication error:', error.message);
        next(error);
    }
});

io.on('connection', socket => {
    const connectedAt = new Date().toISOString();

    // Track active user
    activeUsers.set(socket.user.userId, {
        socketId: socket.id,
        userInfo: {
            userId: socket.user.userId,
            email: socket.user.email
        },
        projectId: socket.project._id,
        connectedAt: connectedAt
    });

    // Simplified connection logging
    console.log(`🚀 User connected: ${socket.user.email} (${socket.user.userId}) | Project: ${socket.project.name} | Active users: ${activeUsers.size}`);

    socket.join(socket.project._id);

    // Message handling with simplified logging
    socket.on('project-message',(data)=>{
        console.log(`📨 Message from ${socket.user.email}: "${data.message}" | Project: ${socket.project.name}`);

        // Broadcast message to other users in the project
        socket.broadcast.to(socket.project._id).emit('project-message', {
            ...data,
            userId: socket.user.userId,
            userEmail: socket.user.email,
            timestamp: new Date().toISOString()
        });
    });

    // Disconnect handling with simplified logging
    socket.on('disconnect', (reason) => {
        activeUsers.delete(socket.user.userId);
        console.log(`❌ User disconnected: ${socket.user.email} | Reason: ${reason} | Remaining: ${activeUsers.size}`);
    });

    // Handle user status check
    socket.on('check-user-status', (userId) => {
        const isConnected = activeUsers.has(userId);
        socket.emit('user-status', { userId, isConnected });
    });

    // Handle getting all active users
    socket.on('get-active-users', () => {
        const users = Array.from(activeUsers.values()).map(user => ({
            userId: user.userInfo.userId,
            email: user.userInfo.email,
            projectId: user.projectId,
            connectedAt: user.connectedAt
        }));
        socket.emit('active-users', users);
    });
});

// Utility function to check if user is connected (can be used in routes)
export const isUserConnected = (userId) => {
    return activeUsers.has(userId);
};

// Utility function to get all active users (can be used in routes)
export const getActiveUsers = () => {
    return Array.from(activeUsers.values()).map(user => ({
        userId: user.userInfo.userId,
        email: user.userInfo.email,
        projectId: user.projectId,
        connectedAt: user.connectedAt
    }));
};

const port = process.env.PORT || 3000;
server.listen(port, () => {
    console.log(`🚀 Server is running on port ${port}`);
    console.log(`🔌 Socket.IO server ready for connections`);
});