import dotenv from 'dotenv';
dotenv.config();
import http from 'http';
import app from './app.js';
import client from './services/redis.services.js';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import projectModel from './models/project.model.js';

mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });

const server = http.createServer(app);

const io = new Server(server,{
    cors: {
        origin: 'http://localhost:5173',
        methods: ['GET', 'POST']
    }
});

// Track active users
const activeUsers = new Map(); // userId -> { socketId, userInfo, projectId, connectedAt }

io.use(async (socket,next)=>{
    try{

        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.split(' ')[1];
        const projectId = socket.handshake.headers?.projectId || socket.handshake.query?.projectId;

        if(!mongoose.Types.ObjectId.isValid(projectId)){
            return next(new Error('Invalid project ID'));
        }

        socket.project = await projectModel.findById(projectId);
        if(!socket.project){
            return next(new Error('Project not found'));
        }

        if(!token){
            return next(new Error('Unauthorized'));
        }

        const decoded = jwt.verify(token,process.env.JWT_SECRET);
        if(!decoded){
            return next(new Error('Unauthorized'));
        }
        socket.user = decoded;
        next();

    }catch(error){
        next(error);
    }
});

io.on('connection', socket => {
    const connectedAt = new Date().toISOString();

    // Track active user
    activeUsers.set(socket.user.userId, {
        socketId: socket.id,
        userInfo: {
            userId: socket.user.userId,
            email: socket.user.email
        },
        projectId: socket.project._id,
        connectedAt: connectedAt
    });

    // Enhanced connection logging with user details
    console.log('🚀 User connected:');
    console.log('   Socket ID:', socket.id);
    console.log('   User ID:', socket.user.userId);
    console.log('   User Email:', socket.user.email);
    console.log('   Project ID:', socket.project._id);
    console.log('   Project Name:', socket.project.name);
    console.log('   Connected at:', connectedAt);
    console.log('   Total active users:', activeUsers.size);
    console.log('-----------------------------------');

    socket.join(socket.project._id);

    // Enhanced message logging
    socket.on('project-message',(data)=>{
        console.log('📨 Message received:');
        console.log('   From User ID:', socket.user.userId);
        console.log('   From User Email:', socket.user.email);
        console.log('   Project ID:', socket.project._id);
        console.log('   Message:', data);
        console.log('   Timestamp:', new Date().toISOString());
        console.log('-----------------------------------');

        // Broadcast message to other users in the project
        socket.broadcast.to(socket.project._id).emit('project-message', {
            ...data,
            userId: socket.user.userId,
            userEmail: socket.user.email,
            timestamp: new Date().toISOString()
        });
    });

    // Enhanced disconnect logging
    socket.on('disconnect', (reason) => {
        // Remove user from active users tracking
        activeUsers.delete(socket.user.userId);

        console.log('❌ User disconnected:');
        console.log('   Socket ID:', socket.id);
        console.log('   User ID:', socket.user.userId);
        console.log('   User Email:', socket.user.email);
        console.log('   Project ID:', socket.project._id);
        console.log('   Reason:', reason);
        console.log('   Disconnected at:', new Date().toISOString());
        console.log('   Remaining active users:', activeUsers.size);
        console.log('-----------------------------------');
    });

    // Handle user status check
    socket.on('check-user-status', (userId) => {
        const isConnected = activeUsers.has(userId);
        socket.emit('user-status', { userId, isConnected });
        console.log(`📊 User status check - User ID: ${userId}, Connected: ${isConnected}`);
    });

    // Handle getting all active users
    socket.on('get-active-users', () => {
        const users = Array.from(activeUsers.values()).map(user => ({
            userId: user.userInfo.userId,
            email: user.userInfo.email,
            projectId: user.projectId,
            connectedAt: user.connectedAt
        }));
        socket.emit('active-users', users);
        console.log(`📊 Active users requested - Count: ${users.length}`);
    });
});

// Utility function to check if user is connected (can be used in routes)
export const isUserConnected = (userId) => {
    return activeUsers.has(userId);
};

// Utility function to get all active users (can be used in routes)
export const getActiveUsers = () => {
    return Array.from(activeUsers.values()).map(user => ({
        userId: user.userInfo.userId,
        email: user.userInfo.email,
        projectId: user.projectId,
        connectedAt: user.connectedAt
    }));
};

const port = process.env.PORT || 3000;
server.listen(port, () => {
    console.log(`🚀 Server is running on port ${port}`);
    console.log(`🔌 Socket.IO server ready for connections`);
});