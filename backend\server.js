import dotenv from 'dotenv';
dotenv.config();
import http from 'http';
import app from './app.js';
import client from './services/redis.services.js';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import mongoose from 'mongoose';
import projectModel from './models/project.model.js';

mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });

const server = http.createServer(app);

const io = new Server(server,{
    cors: {
        origin: 'http://localhost:5173',
        methods: ['GET', 'POST']
    }
});

io.use(async (socket,next)=>{
    try{

        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.split(' ')[1];
        const projectId= socket.handshake.headers?.projectId;

        if(!mongoose.Types.ObjectId.isValid(projectId)){
            return next(new Error('Invalid project ID'));
        }

        socket.project = await projectModel.findById(projectId);
        if(!socket.project){
            return next(new Error('Project not found'));
        }

        if(!token){
            return next(new Error('Unauthorized'));
        }

        const decoded = jwt.verify(token,process.env.JWT_SECRET);
        if(!decoded){
            return next(new Error('Unauthorized'));
        }
        socket.user = decoded;
        next();

    }catch(error){
        next(error);
    }
});

io.on('connection', socket => {
    console.log('a User Connected')
    socket.join(socket.project._id); 
    socket.on('project-message',(data)=>{
        socket.broadcast.to(socket.project._id).emit('project-message',data);
    })

    socket.on('event', data => { /* handle event */ });
    socket.on('disconnect', () => { /* handle disconnect */ });
});

const port = process.env.PORT || 3000;
server.listen(port, () => {
    console.log(`🚀 Server is running on port ${port}`);
});